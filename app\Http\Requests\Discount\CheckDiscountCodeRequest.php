<?php

namespace App\Http\Requests\Discount;

use App\Models\Discount\DiscountCode;
use Illuminate\Foundation\Http\FormRequest;

class CheckDiscountCodeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'code' => 'string|required',
        ];
    }
    public function withValidator($validator)
    {

        $validator->after(function ($validator) {
            $user = auth()->user();
            $cart = $user->cart;
            $discountCode = DiscountCode::where('code', $this->input('code'))->first();

            if (!$discountCode) {
                $validator->errors()->add('discount code', __('messages.discount_code.not_valid'));
                return;
            }

            $isValidForCart = false;
            if ($discountCode->product_id === null) {
                $isValidForCart = $discountCode->isValid();
            } else {
                $cartProductIds = $cart->items->pluck('product_id')->toArray();
                if (in_array($discountCode->product_id, $cartProductIds)) {
                    $isValidForCart = $discountCode->isValid($discountCode->product_id);
                }
            }

            if (!$isValidForCart) {
                $validator->errors()->add('discount code', __('messages.discount_code.not_valid'));
                return;
            }


        });
    }
}
