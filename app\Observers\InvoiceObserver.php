<?php

namespace App\Observers;

use App\Enums\PayTypeEnum;
use App\Models\Shopping\Invoice;

class InvoiceObserver
{
    /**
     * Handle the Invoice "updated" event.
     * Increment discount code usage when invoice status changes to paid.
     * This ensures usage count only increases when payment is actually completed.
     */
    public function updated(Invoice $invoice): void
    {
        // Check if status was changed to PAID
        if ($invoice->isDirty('status') && $invoice->status === PayTypeEnum::PAID) {
            // Check if invoice has a discount code and it's not already at its limit
            if ($invoice->discount_code_id && $invoice->discountCode) {
                $discountCode = $invoice->discountCode;

                // Only increment if not already at usage limit
                if (!$discountCode->hasReachedUsageLimit()) {
                    $discountCode->incrementUseCount();
                }
            }
        }
    }
}
