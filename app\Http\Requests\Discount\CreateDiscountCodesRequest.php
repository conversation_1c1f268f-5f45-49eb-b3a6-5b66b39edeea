<?php

namespace App\Http\Requests\Discount;

use App\Enums\Discount\DiscountTypeEnum;
use App\Rules\JalaliDateRule;
use Illuminate\Foundation\Http\FormRequest;

class CreateDiscountCodesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'code' => ['required', 'string', 'max:30', 'unique:discount_codes,code'],
            'type' => [
                'required',
                DiscountTypeEnum::rule()
            ],
            'amount' => ['required', 'integer', 'min:1'],
            'price_unit_id' => ['required', 'exists:price_units,id'],
            'is_active' => ['sometimes', 'boolean'],
            'phone' => ['nullable', 'string', 'exists:users,phone'],
            'starts_at' => [
                'nullable',
                new JalaliDateRule()
            ],
            'expires_at' => [
                'nullable',
                new JalaliDateRule()
            ],
            'product_id' => ['nullable', 'exists:products,id'],
            'use_count_limit' => ['nullable', 'integer', 'min:1'],
        ];
    }
}
