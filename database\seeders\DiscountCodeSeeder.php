<?php

namespace Database\Seeders;

use App\Enums\Discount\DiscountTypeEnum;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use App\Models\Discount\DiscountCode;
use App\Models\User\User; // assumes you have a users table

class DiscountCodeSeeder extends Seeder
{
    public function run(): void
    {
        // Get default price unit (تومان)
        $defaultPriceUnit = \App\Models\Product\PriceUnit::getDefault();
        if (!$defaultPriceUnit) {
            $this->command->error('No default price unit found. Please run PriceUnitSeeder first.');
            return;
        }

        /** ----------------------------------------------------------------
         *  1) Global codes (no user_id)
         * ---------------------------------------------------------------- */
        $this->createCode(
            code: 'GLOBAL10',
            type: DiscountTypeEnum::PERCENT,
            amount: 10,
            isActive: true,
            expiresAt: now()->addDays(30)   // active, future expiry
        );

        $this->createCode(
            code: 'GLOBAL20OFF',
            type: DiscountTypeEnum::PERCENT,
            amount: 20,
            isActive: false,                // inactive, already expired
            expiresAt: now()->subDays(7)
        );

        $this->createCode(
            code: 'GLOBALFIX50K',
            type: DiscountTypeEnum::FIXED,
            amount: 50_000,
            isActive: true,                 // active, no expiry
            expiresAt: null
        );

        // Add examples with new fields
        $this->createCode(
            code: 'FUTURE20',
            type: DiscountTypeEnum::PERCENT,
            amount: 20,
            isActive: true,
            startsAt: now()->addDays(7),    // starts in 7 days
            expiresAt: now()->addDays(30)
        );

        // Limited use discount code
        $this->createCode(
            code: 'LIMITED50',
            type: DiscountTypeEnum::PERCENT,
            amount: 50,
            isActive: true,
            expiresAt: now()->addDays(30),
            useCountLimit: 10  // Only 10 uses allowed
        );

        // Product-specific discount (if products exist)
        $firstProduct = \App\Models\Product\Product::first();
        if ($firstProduct) {
            $this->createCode(
                code: 'PRODUCT10',
                type: DiscountTypeEnum::PERCENT,
                amount: 10,
                isActive: true,
                productId: $firstProduct->id,
                expiresAt: now()->addDays(15),
                useCountLimit: 5  // Limited to 5 uses for this product
            );
        }

        /** ----------------------------------------------------------------
         *  2) User-specific codes (attach to first 3 users found)
         * ---------------------------------------------------------------- */
        User::query()
            ->limit(3)
            ->get()
            ->each(function (User $user, int $index) {
                // Percent code
                $this->createCode(
                    code: "U{$user->id}_PERC",
                    type: DiscountTypeEnum::PERCENT,
                    amount: 15,
                    userId: $user->id,
                    isActive: true,
                    expiresAt: now()->addDays(14)
                );

                // Fixed code
                $this->createCode(
                    code: "U{$user->id}_FIX",
                    type: DiscountTypeEnum::FIXED,
                    amount: 100_000,
                    userId: $user->id,
                    isActive: true,
                    expiresAt: null
                );
            });
    }

    /** --------------------------------------------------------------------
     *  Helper to keep run() tidy
     * -------------------------------------------------------------------*/
    private function createCode(
        string $code,
        DiscountTypeEnum $type,
        int $amount,
        ?int $userId = null,
        bool $isActive = true,
        ?\DateTimeInterface $startsAt = null,
        ?\DateTimeInterface $expiresAt = null,
        ?int $productId = null,
        int $useCount = 0,
        ?int $useCountLimit = null
    ): void {
        $defaultPriceUnit = \App\Models\Product\PriceUnit::getDefault();

        DiscountCode::updateOrCreate([
            'code' => $code,
        ], [
            'uuid' => Str::uuid()->toString(),
            'user_id' => $userId,
            'type' => $type,
            'amount' => $amount,
            'is_active' => $isActive,
            'starts_at' => $startsAt,
            'expires_at' => $expiresAt,
            'product_id' => $productId,
            'use_count' => $useCount,
            'use_count_limit' => $useCountLimit,
            'price_unit_id' => $defaultPriceUnit?->id,
        ]);
    }
}
