<?php

namespace App\Services\Actions\Discount;

use App\Enums\Discount\DiscountTypeEnum;
use App\Models\Discount\DiscountCode;
use App\Models\User\User;
use App\Traits\Pagination;
use Illuminate\Support\Str;

class CreateDiscountCode
{
    use Pagination;

    public function handle(array $data): DiscountCode
    {
        $userId = null;
        if (!empty($data['phone'])) {
            $userId = User::where('phone', $data['phone'])->value('id');
        }
        return DiscountCode::withUser()->create([
            'uuid' => Str::uuid()->toString(),
            'code' => $data['code'],
            'type' => DiscountTypeEnum::fromString($data['type']),
            'amount' => $data['amount'],
            'price_unit_id' => $data['price_unit_id'],
            'is_active' => $data['is_active'] ?? true,
            'user_id' => $userId,
            'starts_at' => isset($data['starts_at'])
                ? CarbonDate($data['starts_at'])
                : null,
            'expires_at' => isset($data['expires_at'])
                ? CarbonDate($data['expires_at'])
                : null,
            'product_id' => $data['product_id'] ?? null,
            'use_count_limit' => $data['use_count_limit'] ?? null,
        ]);
    }
}
