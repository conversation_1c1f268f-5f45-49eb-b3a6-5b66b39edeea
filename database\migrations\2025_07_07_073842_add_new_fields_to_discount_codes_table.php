<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('discount_codes', function (Blueprint $table) {
            $table->timestamp('starts_at')->nullable()->after('is_active');
            $table->foreignId('product_id')->nullable()->after('expires_at')->constrained('products')->onDelete('cascade');
            $table->integer('use_count')->default(0)->after('product_id');
            $table->integer('use_count_limit')->nullable()->after('use_count');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('discount_codes', function (Blueprint $table) {
            $table->dropForeign(['product_id']);
            $table->dropColumn(['starts_at', 'product_id', 'use_count', 'use_count_limit']);
        });
    }
};
