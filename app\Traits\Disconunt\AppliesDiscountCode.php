<?php

namespace App\Traits\Disconunt;

use App\Enums\Discount\DiscountTypeEnum;
use App\Models\Discount\DiscountCode;

trait AppliesDiscountCode
{
    protected function applyDiscountToCart(string $code, $cartTotal, $min = 10_000)
    {
        $discountCode = DiscountCode::where('code', $code)->first();

        $discountAmount = 0;
        if ($discountCode->type == DiscountTypeEnum::FIXED) {
            $discountAmount = $discountCode->amount;
            $offPrice = max($cartTotal - $discountCode->amount, $min);
        } else if ($discountCode->type == DiscountTypeEnum::PERCENT) {
            $percent = $discountCode->amount;
            $discountAmount = ($cartTotal * $percent) / 100;
            $offPrice = max($cartTotal - $discountAmount, $min);
        }
        if ($offPrice == 0) {
            $discountAmount = 0;
        }



        return [
            $discountAmount,
            $offPrice,
            $discountCode
        ];
    }
}
