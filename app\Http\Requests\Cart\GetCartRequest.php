<?php

namespace App\Http\Requests\Cart;

use App\Models\Discount\DiscountCode;
use Illuminate\Foundation\Http\FormRequest;

/**
 * Get Cart Request
 *
 * Validates the request data for retrieving the shopping cart.
 */
class GetCartRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only authenticated users can view their cart
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'discount_code' => 'sometimes|string|exists:discount_codes,code'
        ];
    }

    public function withValidator($validator)
    {

        $validator->after(function ($validator) {
            if (!$this->input('discount_code'))
                return;
            $user = auth()->user();
            $cart = $user->cart;
            $discountCode = DiscountCode::where('code', $this->input('discount_code'))->first();

            if (!$discountCode) {
                $validator->errors()->add('discount code', __('messages.discount_code.not_valid'));
                return;
            }

            $isValidForCart = false;
            if ($discountCode->product_id === null) {
                $isValidForCart = $discountCode->isValid();
            } else {
                $cartProductIds = $cart->items->pluck('product_id')->toArray();
                if (in_array($discountCode->product_id, $cartProductIds)) {
                    $isValidForCart = $discountCode->isValid($discountCode->product_id);
                }
            }

            if (!$isValidForCart) {
                $validator->errors()->add('discount code', __('messages.discount_code.not_valid'));
                return;
            }


        });
    }
}
