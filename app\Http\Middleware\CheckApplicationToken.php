<?php

namespace App\Http\Middleware;

use App\Models\Application;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\Response;

class CheckApplicationToken
{
    public function handle(Request $request, Closure $next)
    {

        if (App::environment('local')) {
            return $next($request);

        }
        $token = $request->header('X-Application-Token');

        if (!$token || !($application = $this->isValidToken($token))) {

            return response()->json([
                'success' => false,
                'data' => [
                    'message' => 'دسترسی غیرمجاز به سرویس ها',
                ],
                'status' => Response::HTTP_FORBIDDEN,
            ], Response::HTTP_FORBIDDEN);

        }

        // ذخیره‌ی مقدار در app() برای استفاده‌ی گلوبال در درخواست جاری
        app()->instance('authenticated_application', $application);

        return $next($request);
    }

    protected function isValidToken($token)
    {
        return Cache::remember("application_token_{$token}", now()->addDays(30), function () use ($token) {
            return Application::select('source', 'token', 'status', 'pattern_login')->where('status', true)->where('token', $token)->first();
        });
    }
}