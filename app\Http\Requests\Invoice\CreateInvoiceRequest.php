<?php

namespace App\Http\Requests\Invoice;

use App\Models\Discount\DiscountCode;
use App\Models\Product\ProductVariant;
use App\Models\Shopping\ShoppingCart;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * Create Invoice Request
 *
 * Validates the request data for creating a new invoice.
 */
class CreateInvoiceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [

            'address_id' => [
                'required',
                'int',
                Rule::exists('addresses', 'id')->where(function ($query) {
                    $query->where('user_id', auth()->id());
                }),
            ],
            'discount_code' => 'nullable|string|exists:discount_codes,code'
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'address_id.required' => __('messages.invoice.address_required'),
            'address_id.exists' => __('messages.invoice.address_not_found'),
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param \Illuminate\Validation\Validator $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $user = auth()->user();
            $userId = $user->id;

            if ($user->full_name == null || $user->national_code == null) {
                $validator->errors()->add('profile', __('messages.user.profile_not_complete'));
                return;
            }
            // Check if the user's cart has items
            $cart = ShoppingCart::where('user_id', $userId)->first();

            // If cart doesn't exist or has no items, add error
            if (!$cart || $cart->items()->count() == 0) {
                $validator->errors()->add('cart', __('messages.cart.cart_empty'));
                return;
            }
            if ($this->input('discount_code')) {
                $discountCode = DiscountCode::where('code', $this->input('discount_code'))->first();

                $isValidForCart = false;
                if ($discountCode->product_id === null) {
                    $isValidForCart = $discountCode->isValid();
                } else {
                    $cartProductIds = $cart->items->pluck('product_id')->toArray();
                    if (in_array($discountCode->product_id, $cartProductIds)) {
                        $isValidForCart = $discountCode->isValid($discountCode->product_id);
                    }
                }

                if (!$isValidForCart) {
                    $validator->errors()->add('discount code', __('messages.discount_code.not_valid'));
                    return;
                }
            }
            // Check stock availability for each cart item
            $cartItems = $cart->items;
            foreach ($cartItems as $cartItem) {
                // Get the product variant
                $variant = ProductVariant::find($cartItem->product_variant_id);

                // If variant doesn't exist, add error
                if (!$variant) {
                    $validator->errors()->add('variant', __('messages.cart.variant_not_found'));
                    return;
                }
                // Check if there's enough stock
                if ($variant->getCurrentQuantityAttribute() < $cartItem->quantity) {
                    $validator->errors()->add($cartItem->title, __('messages.cart.insufficient_stock'));
                    return;
                }
            }
        });
    }

    // No need to prepare data for validation as we'll get the user ID directly in the action
}
